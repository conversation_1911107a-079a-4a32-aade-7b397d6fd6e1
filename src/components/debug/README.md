# 调试组件

## DebugDrawer - 调试抽屉

右侧抽屉形式的调试面板，集成了多种调试功能，方便开发者进行性能监控和问题排查。

### 功能特性

#### 🎯 性能概览

- 便签数量和虚拟化阈值监控
- 设备性能评分显示
- 连接线数量统计
- 内存使用情况概览

#### 📊 虚拟化监控

- 实时显示便签数量与虚拟化阈值对比
- 设备性能等级和评分
- 智能性能建议
- 虚拟化状态颜色指示

#### 🔗 连接线监控

- 连接线总数、普通连接、溯源连接统计
- 更新频率和性能指标
- 平均/最大更新时间监控
- 节流命中次数统计
- 连接线调试操作（强制更新、重置统计、清理连接）

#### 🧠 内存监控

- JS 堆内存使用情况
- 内存使用率百分比
- 内存使用警告提示
- 支持浏览器内存 API 检测

#### ℹ️ 系统信息

- 用户代理信息
- CPU 核心数
- 设备像素比
- 屏幕分辨率和视口大小
- 在线状态
- 语言和时区信息

#### ⚡ 快捷操作

- 手动触发垃圾回收（需要 --expose-gc 标志）
- 清理控制台
- 导出性能报告到控制台

### 使用方式

```tsx
import { DebugDrawer } from "./components/debug";

// 在应用中使用
<DebugDrawer />;
```

### 显示条件

- 仅在开发环境 (`NODE_ENV === 'development'`) 中显示
- 通过右下角的悬浮按钮打开调试抽屉
- 悬浮按钮会根据性能状态显示不同颜色的徽章

### 交互优化

- **无遮罩设计**：抽屉打开时不会阻挡便签操作，可以同时进行调试和便签编辑
- **多种关闭方式**：
  - 点击抽屉标题栏的关闭按钮
  - 点击抽屉内容区右上角的关闭按钮
  - 按 ESC 键快速关闭
- **层级管理**：合理的 z-index 设置，确保拖拽便签始终在抽屉之上

### 颜色指示

- 🟢 绿色：性能良好
- 🟡 橙色：性能一般或接近阈值
- 🔴 红色：性能差或超出阈值

### 扩展性

调试抽屉采用模块化设计，可以轻松添加新的调试功能：

1. 在 `getCollapseItems` 函数中添加新的折叠面板项
2. 添加相应的状态管理和数据更新逻辑
3. 实现对应的 UI 组件和交互功能

这样的设计使得后续添加新的调试功能变得非常简单和灵活。
