# 思维链容器自适应高度功能

## 功能概述

为便签中的思维链显示容器实现了简洁的自适应高度调整功能，固定200px作为基础高度，便签缩小时根据高度限制自动缩小。

## 设计理念

### 简化策略
- **固定基础高度**：200px作为思维链容器的理想显示高度
- **智能缩小**：当便签高度不足时，自动缩小以适应空间
- **实时响应**：便签尺寸变化时，思维链容器高度实时调整

## 技术实现

### 高度计算逻辑
```typescript
const calculateThinkingChainHeight = (noteHeight: number) => {
  // 固定基础高度200px
  const baseMaxHeight = 200;
  
  // 计算便签中各部分占用的空间
  const usedSpace = 40 + 24 + 36 + 16 + 12 + 60; // 总共188px
  const availableHeight = noteHeight - usedSpace;
  
  // 取基础高度和可用高度的较小值
  const maxHeight = Math.min(baseMaxHeight, Math.max(availableHeight, 40));
  const minHeight = Math.min(60, maxHeight);
  
  return {
    '--thinking-max-height': `${maxHeight}px`,
    '--thinking-min-height': `${minHeight}px`,
    '--thinking-max-height-compact': `${maxHeight * 0.9}px`,
    '--thinking-min-height-compact': `${minHeight * 0.9}px`,
  };
};
```

### CSS变量控制
```css
.thinking-chain-in-note .thinking-process-section {
  max-height: var(--thinking-max-height, 200px);
  min-height: var(--thinking-min-height, 60px);
}

.thinking-chain-in-note.compact .thinking-process-section {
  max-height: var(--thinking-max-height-compact, 180px);
  min-height: var(--thinking-min-height-compact, 54px);
}
```

## 用户体验

### 空间利用
- **大便签**：思维链显示200px高度，提供充足的阅读空间
- **小便签**：思维链自动缩小，确保便签内容有足够显示空间
- **平衡设计**：思维链和便签内容之间保持合理的空间分配

### 响应式调整
- **拖拽调整**：用户调整便签大小时，思维链高度实时适配
- **性能优化**：使用React.useCallback缓存计算函数
- **兼容性**：完全兼容现有的紧凑模式和便签内显示模式

## 使用场景

1. **标准使用**：便签高度充足时，思维链显示200px高度
2. **空间受限**：便签较小时，思维链自动缩小但保持可读性
3. **动态调整**：用户调整便签大小时，思维链高度跟随变化

## 优势

- **简洁明了**：逻辑简单，易于理解和维护
- **用户友好**：无需配置，自动适应各种使用场景
- **性能良好**：计算开销小，不影响界面响应速度
- **向后兼容**：完全兼容现有功能，无破坏性变更
